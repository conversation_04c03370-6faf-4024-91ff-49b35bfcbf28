# Multi-Leg Option Spread Integration Plan for NautilusTrader

Based on comprehensive research of the NautilusTrader codebase, here's the detailed integration plan for multi-leg option spreads with individual leg fills.

## 1. Architecture Overview

```
Strategy → SpreadOrderManager → ExecutionEngine → ExecutionClient → Venue
    ↓            ↓                    ↓               ↓             ↓
SpreadQuoteProvider → Cache → MessageBus → DataEngine → DataClient → Market Data
```

## 2. Core Components Integration

### 2.1 Instrument System Extension

**Location**: `nautilus_trader/model/instruments/`

**Approach**: Extend existing spread instrument pattern
- **Current**: `FuturesSpread` and `OptionSpread` exist but are atomic instruments
- **Enhancement**: Create `MultiLegOptionSpread` that inherits from existing patterns
- **Integration**: Add to `InstrumentAny` enum in Rust core

```python
# Add to nautilus_trader/model/instruments/multi_leg_option_spread.py
class MultiLegOptionSpread(Instrument):
    """Multi-leg option spread with up to 4 legs, similar to IB combo orders"""
    def __init__(self, legs: List[SpreadLeg], ...):
        # Leverage existing instrument patterns
        super().__init__(...)
```

**Key Integration Points**:
- `cache.add_instrument(spread)` - Standard instrument registration
- Follows existing `FuturesSpread`/`OptionSpread` patterns in crates/model/src/instruments/

### 2.2 Order Management Integration

**Location**: `nautilus_trader/execution/`

**Approach**: Extend existing order management without breaking current flow

```python
# Custom ExecutionClient extension
class SpreadExecutionClient(ExecutionClient):
    """Handles multi-leg spread orders"""
    
    def __init__(self):
        super().__init__()
        self.spread_managers: Dict[InstrumentId, SpreadOrderManager] = {}
    
    async def submit_order(self, command: SubmitOrder):
        if isinstance(command.order.instrument_id, MultiLegOptionSpread):
            # Create SpreadOrderManager and decompose into leg orders
            manager = SpreadOrderManager(spread)
            leg_orders = manager.submit_spread_order(...)
            
            # Submit each leg order through standard flow
            for leg_order in leg_orders:
                await super().submit_order(SubmitOrder(...))
        else:
            # Standard order flow
            await super().submit_order(command)
```

**Integration Points**:
- **ExecutionEngine**: Route spread orders to SpreadExecutionClient
- **OrderManager**: Handle leg order state tracking
- **Message Bus**: Publish spread completion events

### 2.3 Data Flow Integration

**Location**: `nautilus_trader/data/engine.pyx`

**Approach**: Leverage existing subscription and caching patterns

```python
class SpreadQuoteProvider:
    def __init__(self, cache: Cache, msgbus: MessageBus):
        self.cache = cache
        self.msgbus = msgbus
        
        # Subscribe to leg quotes using existing patterns
        self.msgbus.subscribe(
            topic=f"data.quotes.{venue}.{symbol}",
            handler=self.on_quote_update,
            priority=5  # Higher than portfolio (10) for spread calculations
        )
```

**Integration Points**:
- **DataEngine**: Standard subscription via `subscribe_quote_ticks()`
- **Cache**: Store spread quotes alongside instrument quotes
- **Message Bus**: Publish spread quotes with custom data types

### 2.4 Strategy Integration

**Location**: `nautilus_trader/trading/strategy.pyx`

**Approach**: Extend Strategy base class with spread-specific methods

```python
# Extension to Strategy class
def register_spread(self, spread: MultiLegOptionSpread):
    """Register spread for trading with auto-quote generation"""
    self.cache.add_instrument(spread)
    
    # Subscribe to leg data
    for leg in spread.legs:
        self.subscribe_quote_ticks(leg.instrument_id)
        self.subscribe_data(DataType(GreeksData), leg.instrument_id)
    
    # Create spread quote provider
    provider = SpreadQuoteProvider(spread, self.greeks)
    self._spread_providers[spread.id] = provider

def submit_spread_order(self, spread_id: InstrumentId, quantity: Quantity):
    """Submit spread order - decomposed into leg orders"""
    manager = self._spread_managers[spread_id]
    return manager.submit_spread_order(quantity, self.order_factory, self)
```

## 3. Extension Points Utilized

### 3.1 Custom Data Types
**Pattern**: Use `@customdataclass` decorator for spread-specific data
```python
@customdataclass
class SpreadQuote:
    spread_id: InstrumentId
    bid: Price
    ask: Price
    theoretical_mid: Price
    portfolio_vega: float
```

### 3.2 Factory Pattern
**Pattern**: Create custom execution client factory
```python
class SpreadExecutionClientFactory:
    def create(self, config) -> SpreadExecutionClient:
        return SpreadExecutionClient(...)

# Register with node
node.add_exec_client_factory("SPREAD", SpreadExecutionClientFactory)
```

### 3.3 Message Bus Integration
**Pattern**: Leverage existing pub/sub for spread events
```python
# Publish spread completion
self.msgbus.publish(
    topic=f"events.spread.{spread_id}",
    msg=SpreadOrderCompleted(spread_id, avg_fill_price)
)
```

## 4. Greeks Integration

**Location**: `nautilus_trader/model/greeks.pyx`

**Approach**: Extend existing GreeksCalculator

```python
# Extension to GreeksCalculator
def portfolio_spread_greeks(
    self, 
    spread: MultiLegOptionSpread,
    vega_multiplier: float = 1.0
) -> SpreadGreeks:
    """Calculate portfolio Greeks for multi-leg spread"""
    total_vega = 0.0
    
    for leg in spread.legs:
        leg_greeks = self.instrument_greeks(leg.instrument_id)
        total_vega += leg_greeks.vega * leg.ratio
    
    return SpreadGreeks(
        vega=total_vega,
        spread_width=abs(total_vega) / 100.0 * vega_multiplier
    )
```

## 5. Implementation Phases

### Phase 1: Core Infrastructure
1. **Instrument Extension** - Add `MultiLegOptionSpread` to model
2. **Data Types** - Create `SpreadLeg`, `SpreadQuote` custom data classes
3. **Basic Integration** - Register with cache and message bus

### Phase 2: Order Management
1. **SpreadOrderManager** - Individual leg tracking
2. **ExecutionClient Extension** - Order decomposition
3. **State Management** - Partial fill handling

### Phase 3: Pricing Engine
1. **SpreadPricingEngine** - Greeks-based pricing
2. **QuoteProvider** - Real-time spread quotes
3. **Cache Integration** - Store spread market data

### Phase 4: Strategy Integration
1. **Strategy Extensions** - Spread-specific methods
2. **Auto-Quote System** - Market making capabilities
3. **Risk Management** - Portfolio Greeks monitoring

## 6. Backwards Compatibility

**Approach**: Additive changes only
- Existing order flow unchanged
- New functionality through extension patterns
- Optional spread features for strategies

## 7. Testing Strategy

### 7.1 Unit Tests
- **SpreadOrderManager**: Individual leg fill scenarios
- **PricingEngine**: Greeks calculation accuracy
- **Integration**: Cache and message bus interaction

### 7.2 Integration Tests
- **Backtest**: Multi-leg spread strategies
- **Live**: Paper trading with real data
- **Performance**: Latency impact assessment

## 8. Configuration

```python
# Strategy configuration extension
@dataclass
class SpreadStrategyConfig(StrategyConfig):
    spreads: List[Dict[str, Any]]  # Spread definitions
    vega_multiplier: float = 1.0
    auto_quote: bool = True
    max_position_size: int = 100
```

## 9. Migration Path

### 9.1 Existing Users
- Current spread instruments (`FuturesSpread`, `OptionSpread`) continue working
- New multi-leg functionality is opt-in
- Gradual migration through strategy updates

### 9.2 New Users
- Enhanced spread functionality available immediately
- Examples and documentation for common patterns
- Integration with existing Greeks and risk systems

## 10. Performance Considerations

### 10.1 Optimization
- **Caching**: Spread quotes cached with TTL
- **Message Bus**: Batch updates for multiple leg changes
- **Greeks**: Reuse calculations across spreads

### 10.2 Scalability
- **Memory**: Efficient leg storage and indexing
- **CPU**: Vectorized Greeks calculations
- **Network**: Minimal additional data overhead

This integration plan leverages NautilusTrader's robust architecture while adding sophisticated multi-leg spread functionality similar to Interactive Brokers, but with enhanced Greeks-based pricing and risk management capabilities.
