# Multi-Leg Option Spread Design for NautilusTrader
# Allows fills into individual components similar to Interactive Brokers

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from enum import Enum
from decimal import Decimal

from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.model.orders import Order
from nautilus_trader.model.greeks import GreeksCalculator
from nautilus_trader.model.greeks_data import GreeksData
from nautilus_trader.core.uuid import UUID4


class SpreadOrderState(Enum):
    """State of a multi-leg spread order."""
    PENDING = "PENDING"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    FAILED = "FAILED"


@dataclass
class SpreadLeg:
    """Represents a single leg of an option spread."""
    instrument_id: InstrumentId
    ratio: int  # Number of contracts (positive for long, negative for short)
    side: OrderSide
    weight: float = 1.0  # For pricing calculations
    
    @property
    def is_long(self) -> bool:
        return self.ratio > 0
    
    @property
    def abs_ratio(self) -> int:
        return abs(self.ratio)


@dataclass
class MultiLegOptionSpread:
    """
    Represents a multi-leg option spread (up to 4 legs).
    Similar to Interactive Brokers combo orders but with enhanced pricing.
    """
    id: InstrumentId
    strategy_type: str  # e.g., "IRON_CONDOR", "BUTTERFLY", "STRADDLE"
    legs: List[SpreadLeg]
    vega_multiplier: float = 1.0  # Configurable multiplier for bid/ask spread
    auto_quote: bool = True  # Whether to automatically provide quotes
    
    def __post_init__(self):
        if len(self.legs) > 4:
            raise ValueError("Maximum 4 legs supported")
        if len(self.legs) < 2:
            raise ValueError("Minimum 2 legs required")
    
    def theoretical_mid(self, quotes_cache: Dict[InstrumentId, Tuple[Price, Price]]) -> Optional[Price]:
        """Calculate theoretical mid as linear combination of component mids."""
        total_value = 0.0
        
        for leg in self.legs:
            if leg.instrument_id not in quotes_cache:
                return None
                
            bid, ask = quotes_cache[leg.instrument_id]
            mid = (bid + ask) / 2
            
            # Apply leg ratio and side
            leg_contribution = mid * leg.ratio
            total_value += leg_contribution
            
        return Price.from_str(str(total_value))
    
    def portfolio_vega(self, greeks_cache: Dict[InstrumentId, GreeksData]) -> float:
        """Calculate portfolio vega from individual leg vegas."""
        total_vega = 0.0
        
        for leg in self.legs:
            if leg.instrument_id not in greeks_cache:
                continue
                
            leg_greeks = greeks_cache[leg.instrument_id]
            leg_vega = leg_greeks.vega * leg.ratio
            total_vega += leg_vega
            
        return total_vega
    
    def bid_ask_spread_width(self, greeks_cache: Dict[InstrumentId, GreeksData]) -> float:
        """Calculate bid/ask spread width using percent vega."""
        portfolio_vega = self.portfolio_vega(greeks_cache)
        percent_vega = abs(portfolio_vega) / 100.0  # Convert to percent
        return percent_vega * self.vega_multiplier


class SpreadOrderManager:
    """
    Manages multi-leg spread orders with individual leg fills.
    Tracks completion and handles partial fills.
    """
    
    def __init__(self, spread: MultiLegOptionSpread):
        self.spread = spread
        self.leg_orders: Dict[str, Order] = {}
        self.leg_fills: Dict[str, List[Tuple[Quantity, Price]]] = {}
        self.state = SpreadOrderState.PENDING
        self.order_id = UUID4()
        
    def submit_spread_order(
        self, 
        total_quantity: Quantity,
        limit_price: Optional[Price] = None,
        order_factory=None,
        executor=None
    ) -> List[Order]:
        """
        Submit individual leg orders for the spread.
        Returns list of leg orders created.
        """
        leg_orders = []
        
        for leg in self.spread.legs:
            leg_quantity = Quantity.from_int(total_quantity.as_f64() * leg.abs_ratio)
            
            # Create individual leg order
            if limit_price:
                # For limit orders, need to calculate leg prices
                # This would require more sophisticated price allocation
                leg_order = order_factory.limit(
                    instrument_id=leg.instrument_id,
                    order_side=leg.side,
                    quantity=leg_quantity,
                    price=limit_price,  # Simplified - would need proper allocation
                )
            else:
                # Market order
                leg_order = order_factory.market(
                    instrument_id=leg.instrument_id,
                    order_side=leg.side,
                    quantity=leg_quantity,
                )
            
            self.leg_orders[str(leg.instrument_id)] = leg_order
            self.leg_fills[str(leg.instrument_id)] = []
            leg_orders.append(leg_order)
            
            # Submit to executor
            if executor:
                executor.submit_order(leg_order)
        
        return leg_orders
    
    def handle_leg_fill(self, instrument_id: InstrumentId, fill_qty: Quantity, fill_price: Price):
        """Handle a fill on one of the legs."""
        leg_key = str(instrument_id)
        
        if leg_key not in self.leg_fills:
            return
            
        self.leg_fills[leg_key].append((fill_qty, fill_price))
        
        # Check if spread order is complete
        if self._check_completion():
            self.state = SpreadOrderState.COMPLETED
        else:
            self.state = SpreadOrderState.PARTIALLY_FILLED
    
    def _check_completion(self) -> bool:
        """Check if all legs are completely filled."""
        for leg in self.spread.legs:
            leg_key = str(leg.instrument_id)
            if leg_key not in self.leg_orders:
                return False
                
            target_qty = self.leg_orders[leg_key].quantity.as_f64()
            filled_qty = sum(fill[0].as_f64() for fill in self.leg_fills[leg_key])
            
            if filled_qty < target_qty:
                return False
                
        return True
    
    def get_average_fill_price(self) -> Optional[Price]:
        """Calculate average fill price for the spread."""
        if self.state != SpreadOrderState.COMPLETED:
            return None
            
        total_value = 0.0
        
        for leg in self.spread.legs:
            leg_key = str(leg.instrument_id)
            fills = self.leg_fills[leg_key]
            
            leg_total_value = 0.0
            leg_total_qty = 0.0
            
            for qty, price in fills:
                leg_total_value += qty.as_f64() * price.as_f64()
                leg_total_qty += qty.as_f64()
            
            if leg_total_qty > 0:
                leg_avg_price = leg_total_value / leg_total_qty
                # Apply leg ratio and side
                contribution = leg_avg_price * leg.ratio
                total_value += contribution
        
        return Price.from_str(str(total_value))


class SpreadPricingEngine:
    """
    Calculates theoretical prices and bid/ask spreads for multi-leg options.
    Uses Greeks for sophisticated spread calculation.
    """
    
    def __init__(self, greeks_calculator: GreeksCalculator):
        self.greeks_calc = greeks_calculator
        self.quotes_cache: Dict[InstrumentId, Tuple[Price, Price]] = {}
        self.greeks_cache: Dict[InstrumentId, GreeksData] = {}
    
    def update_quote(self, instrument_id: InstrumentId, bid: Price, ask: Price):
        """Update quote for a leg instrument."""
        self.quotes_cache[instrument_id] = (bid, ask)
    
    def update_greeks(self, instrument_id: InstrumentId, greeks: GreeksData):
        """Update Greeks for a leg instrument."""
        self.greeks_cache[instrument_id] = greeks
    
    def calculate_spread_quote(self, spread: MultiLegOptionSpread) -> Optional[Tuple[Price, Price]]:
        """
        Calculate bid/ask for the spread.
        Mid = linear combination of leg mids
        Spread = percent vega * vega_multiplier
        """
        theoretical_mid = spread.theoretical_mid(self.quotes_cache)
        if not theoretical_mid:
            return None
            
        spread_width = spread.bid_ask_spread_width(self.greeks_cache)
        half_spread = spread_width / 2
        
        bid = Price.from_str(str(theoretical_mid.as_f64() - half_spread))
        ask = Price.from_str(str(theoretical_mid.as_f64() + half_spread))
        
        return (bid, ask)


class SpreadQuoteProvider:
    """
    Provides real-time quotes for multi-leg spreads.
    Monitors leg quotes and publishes spread quotes.
    """
    
    def __init__(self, spread: MultiLegOptionSpread, pricing_engine: SpreadPricingEngine):
        self.spread = spread
        self.pricing = pricing_engine
        self.subscribers = []
        
    def on_quote_update(self, instrument_id: InstrumentId, bid: Price, ask: Price):
        """Handle quote update for a leg instrument."""
        self.pricing.update_quote(instrument_id, bid, ask)
        
        # Check if this instrument is part of our spread
        if any(leg.instrument_id == instrument_id for leg in self.spread.legs):
            self._publish_spread_quote()
    
    def on_greeks_update(self, instrument_id: InstrumentId, greeks: GreeksData):
        """Handle Greeks update for a leg instrument."""
        self.pricing.update_greeks(instrument_id, greeks)
        
        # Check if this instrument is part of our spread
        if any(leg.instrument_id == instrument_id for leg in self.spread.legs):
            self._publish_spread_quote()
    
    def _publish_spread_quote(self):
        """Calculate and publish spread quote."""
        spread_quote = self.pricing.calculate_spread_quote(self.spread)
        if spread_quote:
            bid, ask = spread_quote
            # Publish to subscribers (strategies, market makers, etc.)
            for callback in self.subscribers:
                callback(self.spread.id, bid, ask)
    
    def subscribe(self, callback):
        """Subscribe to spread quote updates."""
        self.subscribers.append(callback)


# Usage Example
def create_iron_condor_example():
    """Example: Create an Iron Condor spread."""
    
    # Define the 4 legs of an Iron Condor
    legs = [
        SpreadLeg(
            instrument_id=InstrumentId.from_str("SPY240315C00450000.SMART"),
            ratio=1,  # Buy 1 call
            side=OrderSide.BUY
        ),
        SpreadLeg(
            instrument_id=InstrumentId.from_str("SPY240315C00460000.SMART"),
            ratio=-1,  # Sell 1 call
            side=OrderSide.SELL
        ),
        SpreadLeg(
            instrument_id=InstrumentId.from_str("SPY240315P00440000.SMART"),
            ratio=1,  # Buy 1 put
            side=OrderSide.BUY
        ),
        SpreadLeg(
            instrument_id=InstrumentId.from_str("SPY240315P00430000.SMART"),
            ratio=-1,  # Sell 1 put
            side=OrderSide.SELL
        ),
    ]
    
    # Create the spread
    iron_condor = MultiLegOptionSpread(
        id=InstrumentId.from_str("SPY_IC_240315.SMART"),
        strategy_type="IRON_CONDOR",
        legs=legs,
        vega_multiplier=1.2,  # Slightly wider spreads
        auto_quote=True
    )
    
    return iron_condor


# Integration with NautilusTrader Strategy
class MultiLegSpreadStrategy:
    """
    Strategy that trades multi-leg option spreads.
    Integrates with existing NautilusTrader infrastructure.
    """
    
    def __init__(self, config):
        self.spreads: Dict[InstrumentId, MultiLegOptionSpread] = {}
        self.order_managers: Dict[InstrumentId, SpreadOrderManager] = {}
        self.pricing_engine = None
        self.quote_providers: Dict[InstrumentId, SpreadQuoteProvider] = {}
    
    def register_spread(self, spread: MultiLegOptionSpread):
        """Register a new spread for trading."""
        self.spreads[spread.id] = spread
        
        # Create order manager
        self.order_managers[spread.id] = SpreadOrderManager(spread)
        
        # Create quote provider if auto-quoting enabled
        if spread.auto_quote:
            quote_provider = SpreadQuoteProvider(spread, self.pricing_engine)
            self.quote_providers[spread.id] = quote_provider
            
            # Subscribe to leg quotes
            for leg in spread.legs:
                # This would integrate with NautilusTrader's subscription system
                pass
    
    def submit_spread_order(self, spread_id: InstrumentId, quantity: Quantity, limit_price: Optional[Price] = None):
        """Submit a spread order."""
        if spread_id not in self.order_managers:
            raise ValueError(f"Spread {spread_id} not registered")
            
        manager = self.order_managers[spread_id]
        return manager.submit_spread_order(quantity, limit_price)
    
    def on_fill(self, instrument_id: InstrumentId, fill_qty: Quantity, fill_price: Price):
        """Handle fill from any leg."""
        # Find which spread this leg belongs to
        for spread_id, manager in self.order_managers.items():
            if str(instrument_id) in manager.leg_orders:
                manager.handle_leg_fill(instrument_id, fill_qty, fill_price)
                break
