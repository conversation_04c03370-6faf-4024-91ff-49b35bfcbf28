"""
Tests for Interactive Brokers combo bracket order functionality.

This module tests that bracket orders (entry + stop-loss + take-profit) work correctly
with option combo instruments, ensuring all orders get proper BAG contract conversion
and combo mapping storage.
"""

import asyncio
from decimal import Decimal

import pytest

from nautilus_trader.adapters.interactive_brokers.common import IBContract
from nautilus_trader.adapters.interactive_brokers.execution import InteractiveBrokersExecutionClient
from nautilus_trader.common.component import MessageBus
from nautilus_trader.common.component import TestClock
from nautilus_trader.common.factories import OrderFactory
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.execution.messages import SubmitOrderList
from nautilus_trader.model.currencies import USD
from nautilus_trader.model.enums import AccountType
from nautilus_trader.model.enums import ContingencyType
from nautilus_trader.model.enums import OmsType
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.enums import OrderType
from nautilus_trader.model.enums import TimeInForce
from nautilus_trader.model.identifiers import AccountId
from nautilus_trader.model.identifiers import ClientId
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.identifiers import StrategyId
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.model.identifiers import Venue
from nautilus_trader.model.instruments.option_combo import ComboLeg
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.objects import Money
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity
from nautilus_trader.portfolio.portfolio import Portfolio
from nautilus_trader.risk.engine import RiskEngine
from nautilus_trader.test_kit.mocks import MockActor
from nautilus_trader.test_kit.stubs import TestExecStubs


class TestComboBracketOrders:
    """Test bracket orders with option combo instruments."""

    def setup_method(self):
        """Set up test fixtures."""
        self.loop = asyncio.get_event_loop()
        self.clock = TestClock()
        
        # Create message bus and cache
        self.msgbus = MessageBus(
            trader_id=TraderId("TESTER-001"),
            clock=self.clock,
        )
        
        # Create cache
        self.cache = self.msgbus.cache
        
        # Create portfolio
        self.portfolio = Portfolio(
            msgbus=self.msgbus,
            cache=self.cache,
            clock=self.clock,
        )
        
        # Create risk engine
        self.risk_engine = RiskEngine(
            portfolio=self.portfolio,
            msgbus=self.msgbus,
            cache=self.cache,
            clock=self.clock,
        )
        
        # Create mock client
        self.mock_client = MockActor()
        
        # Create execution client
        self.exec_client = InteractiveBrokersExecutionClient(
            loop=self.loop,
            client=self.mock_client,
            msgbus=self.msgbus,
            cache=self.cache,
            clock=self.clock,
            instrument_provider=None,  # Will be mocked
            account_id=AccountId("IB-123456"),
        )
        
        # Create order factory
        self.order_factory = OrderFactory(
            trader_id=TraderId("TESTER-001"),
            strategy_id=StrategyId("TEST-001"),
            clock=self.clock,
        )
        
        # Create test combo instrument
        self.combo_instrument = OptionCombo(
            instrument_id=InstrumentId.from_str("SPY_COMBO.COMBO"),
            raw_symbol="SPY_COMBO",
            legs=[
                ComboLeg(
                    instrument_id=InstrumentId.from_str("SPY240315C00500000.OPRA"),
                    ratio=1,  # BUY call
                ),
                ComboLeg(
                    instrument_id=InstrumentId.from_str("SPY240315P00480000.OPRA"),
                    ratio=-1,  # SELL put
                ),
            ],
            price_precision=2,
            price_increment=Price.from_str("0.01"),
            multiplier=Quantity.from_int(100),
            lot_size=Quantity.from_int(1),
            underlying="SPY",
            activation_ns=0,
            expiration_ns=1710460800000000000,  # 2024-03-15
            ts_event=0,
            ts_init=0,
        )
        
        # Add instrument to cache
        self.cache.add_instrument(self.combo_instrument)

    def test_bracket_order_creation_with_combo_instrument(self):
        """Test that bracket orders can be created with combo instruments."""
        # Create bracket order
        bracket_orders = self.order_factory.bracket(
            instrument_id=self.combo_instrument.id,
            order_side=OrderSide.BUY,
            quantity=Quantity.from_int(1),
            entry_order_type=OrderType.MARKET,
            tp_price=Price.from_str("5.00"),
            sl_trigger_price=Price.from_str("1.00"),
        )
        
        # Verify bracket structure
        assert len(bracket_orders.orders) == 3
        entry_order, sl_order, tp_order = bracket_orders.orders
        
        # All orders should reference the same combo instrument
        assert entry_order.instrument_id == self.combo_instrument.id
        assert sl_order.instrument_id == self.combo_instrument.id
        assert tp_order.instrument_id == self.combo_instrument.id
        
        # Verify order relationships
        assert entry_order.contingency_type == ContingencyType.OTO
        assert sl_order.contingency_type == ContingencyType.OUO
        assert tp_order.contingency_type == ContingencyType.OUO
        
        # Verify parent-child relationships
        assert sl_order.parent_order_id == entry_order.client_order_id
        assert tp_order.parent_order_id == entry_order.client_order_id

    @pytest.mark.asyncio
    async def test_bracket_order_submission_with_combo_mapping(self):
        """Test that bracket order submission stores combo mappings for all orders."""
        # Mock instrument provider
        mock_instrument_provider = MockActor()
        mock_instrument_provider.find = lambda instrument_id: self.combo_instrument
        mock_instrument_provider.get_price_magnifier = lambda instrument_id: 1
        mock_instrument_provider.contract_details = {}
        
        self.exec_client.instrument_provider = mock_instrument_provider
        
        # Mock client methods
        self.mock_client.next_order_id = lambda: 1001
        self.mock_client.place_order = lambda order: None
        
        # Create bracket order
        bracket_orders = self.order_factory.bracket(
            instrument_id=self.combo_instrument.id,
            order_side=OrderSide.BUY,
            quantity=Quantity.from_int(1),
            entry_order_type=OrderType.MARKET,
            tp_price=Price.from_str("5.00"),
            sl_trigger_price=Price.from_str("1.00"),
        )
        
        # Create submit command
        command = SubmitOrderList(
            trader_id=TraderId("TESTER-001"),
            strategy_id=StrategyId("TEST-001"),
            order_list=bracket_orders,
            command_id=UUID4(),
            ts_init=self.clock.timestamp_ns(),
        )
        
        # Submit order list
        await self.exec_client._submit_order_list(command)
        
        # Verify combo mappings were stored for all orders
        combo_mappings = self.exec_client._combo_order_mappings
        
        # Should have 3 mappings (entry, stop-loss, take-profit)
        assert len(combo_mappings) == 3
        
        # Verify each mapping contains correct combo information
        for order_id, mapping in combo_mappings.items():
            assert mapping['combo_instrument'] == self.combo_instrument
            assert len(mapping['leg_instrument_ids']) == 2
            assert len(mapping['leg_sides']) == 2
            assert len(mapping['leg_ratios']) == 2
            
            # Verify leg details
            assert mapping['leg_instrument_ids'][0] == InstrumentId.from_str("SPY240315C00500000.OPRA")
            assert mapping['leg_instrument_ids'][1] == InstrumentId.from_str("SPY240315P00480000.OPRA")
            assert mapping['leg_ratios'][0] == 1  # BUY call
            assert mapping['leg_ratios'][1] == -1  # SELL put
