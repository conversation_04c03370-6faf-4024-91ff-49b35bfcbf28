#!/usr/bin/env python3

"""
Test script for option combo pricing functionality.
"""

import asyncio
from decimal import Decimal

from nautilus_trader.common.component import LiveClock
from nautilus_trader.common.component import Logger
from nautilus_trader.common.component import MessageBus
from nautilus_trader.cache.cache import Cache
from nautilus_trader.model.currencies import USD
from nautilus_trader.model.enums import AssetClass
from nautilus_trader.model.enums import OptionKind
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.identifiers import Symbol
from nautilus_trader.model.instruments.option_contract import OptionContract
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity

from nautilus_trader.model.instruments.option_combo import ComboLeg
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.combo_pricing import ComboPricingEngine


async def main():
    """Test combo pricing functionality."""
    print("=== Combo Pricing Test ===\n")
    
    # Create components
    clock = LiveClock()
    logger = Logger(name="ComboTest")
    msgbus = MessageBus(
        trader_id=None,
        clock=clock,
        logger=logger,
    )
    cache = Cache(
        logger=logger,
        config=None,
    )
    
    # Create pricing engine
    pricing_engine = ComboPricingEngine(
        cache=cache,
        logger=logger,
        clock=clock,
    )
    
    # Create option instruments
    call_520 = OptionContract(
        instrument_id=InstrumentId.from_str("SPY240315C00520000.OPRA"),
        raw_symbol=Symbol("SPY   240315C00520000"),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying="SPY",
        option_kind=OptionKind.CALL,
        strike_price=Price.from_str("520.00"),
        activation_ns=0,
        expiration_ns=1710547200000000000,  # March 15, 2024
        ts_event=0,
        ts_init=0,
    )
    
    put_480 = OptionContract(
        instrument_id=InstrumentId.from_str("SPY240315P00480000.OPRA"),
        raw_symbol=Symbol("SPY   240315P00480000"),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying=Symbol("SPY"),
        option_kind=OptionKind.PUT,
        strike_price=Price.from_str("480.00"),
        activation_ns=0,
        expiration_ns=1710547200000000000,  # March 15, 2024
        ts_event=0,
        ts_init=0,
    )
    
    # Create a simple strangle combo (using signed ratios)
    strangle_legs = [
        ComboLeg(
            instrument_id=call_520.id,
            ratio=-1,  # Negative = SELL
        ),
        ComboLeg(
            instrument_id=put_480.id,
            ratio=-1,  # Negative = SELL
        ),
    ]
    
    strangle = OptionCombo(
        instrument_id=InstrumentId.from_str("SPY_STRANGLE_240315.COMBO"),
        raw_symbol=Symbol("SPY_STRANGLE_240315"),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying=Symbol("SPY"),
        strategy_type="STRANGLE",
        legs=strangle_legs,
        vega_multiplier=Decimal("1.0"),
        activation_ns=0,
        expiration_ns=1710547200000000000,
        ts_event=0,
        ts_init=0,
    )
    
    print(f"Created strangle: {strangle}")
    print(f"Legs: {len(strangle.legs)}")
    for i, leg in enumerate(strangle.legs, 1):
        print(f"  Leg {i}: {leg}")
    
    # Test combo price calculation with mock prices
    leg_prices = {
        call_520.id: Price.from_str("2.50"),
        put_480.id: Price.from_str("1.80"),
    }
    
    combo_price = strangle.calculate_combo_price(leg_prices)
    print(f"\nCombo price calculation:")
    print(f"  Call 520 price: {leg_prices[call_520.id]}")
    print(f"  Put 480 price: {leg_prices[put_480.id]}")
    print(f"  Strangle price (SELL both): {combo_price}")
    
    # Test vega-weighted spread calculation
    leg_vegas = {
        call_520.id: 0.15,
        put_480.id: 0.12,
    }
    
    bid_ask_spread = strangle.calculate_vega_weighted_spread(
        leg_vegas=leg_vegas,
        target_spread=Price.from_str("0.10")
    )
    
    print(f"\nVega-weighted spread calculation:")
    print(f"  Call 520 vega: {leg_vegas[call_520.id]}")
    print(f"  Put 480 vega: {leg_vegas[put_480.id]}")
    print(f"  Target spread: 0.10")
    print(f"  Calculated bid-ask spread: {bid_ask_spread}")
    
    print("\n=== Test Complete ===")


if __name__ == "__main__":
    asyncio.run(main())
