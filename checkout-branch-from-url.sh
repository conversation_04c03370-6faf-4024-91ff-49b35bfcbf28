#!/bin/bash

# Script to checkout a branch from a GitHub URL
# Usage: ./checkout-branch-from-url.sh <github-url>
# Example: ./checkout-branch-from-url.sh https://github.com/stastnypremysl/nautilus_trader/tree/propagate-start-stop-in-request-data

set -e  # Exit on any error

# Function to display usage
usage() {
    echo "Usage: $0 <github-url>"
    echo "Example: $0 https://github.com/stastnypremysl/nautilus_trader/tree/propagate-start-stop-in-request-data"
    echo ""
    echo "The script will:"
    echo "1. Parse the GitHub URL to extract owner, repo, and branch"
    echo "2. Add the fork as a remote (if not already added)"
    echo "3. Fetch from the remote"
    echo "4. Checkout and switch to the branch"
    exit 1
}

# Check if URL is provided
if [ $# -eq 0 ]; then
    echo "Error: No GitHub URL provided"
    usage
fi

URL="$1"

# Validate URL format
if [[ ! "$URL" =~ ^https://github\.com/.+/.+/tree/.+ ]]; then
    echo "Error: Invalid GitHub URL format"
    echo "Expected format: https://github.com/owner/repo/tree/branch-name"
    usage
fi

# Parse the URL to extract components
# Remove https://github.com/ prefix
URL_PATH="${URL#https://github.com/}"

# Split by '/' to get owner, repo, and the rest
IFS='/' read -ra PARTS <<< "$URL_PATH"

if [ ${#PARTS[@]} -lt 4 ]; then
    echo "Error: Invalid URL structure"
    usage
fi

OWNER="${PARTS[0]}"
REPO="${PARTS[1]}"
# Skip "tree" part and get branch name (join remaining parts with '/')
BRANCH_NAME=""
for ((i=3; i<${#PARTS[@]}; i++)); do
    if [ -z "$BRANCH_NAME" ]; then
        BRANCH_NAME="${PARTS[i]}"
    else
        BRANCH_NAME="$BRANCH_NAME/${PARTS[i]}"
    fi
done

echo "Parsed URL:"
echo "  Owner: $OWNER"
echo "  Repository: $REPO"
echo "  Branch: $BRANCH_NAME"
echo ""

# Generate remote name (use owner name, replace special chars with underscores)
REMOTE_NAME=$(echo "$OWNER" | sed 's/[^a-zA-Z0-9]/_/g')

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "Error: Not in a git repository"
    exit 1
fi

# Check if remote already exists
if git remote get-url "$REMOTE_NAME" > /dev/null 2>&1; then
    echo "Remote '$REMOTE_NAME' already exists"
    EXISTING_URL=$(git remote get-url "$REMOTE_NAME")
    EXPECTED_URL="https://github.com/$OWNER/$REPO.git"
    
    if [ "$EXISTING_URL" != "$EXPECTED_URL" ]; then
        echo "Warning: Existing remote URL ($EXISTING_URL) doesn't match expected URL ($EXPECTED_URL)"
        echo "Updating remote URL..."
        git remote set-url "$REMOTE_NAME" "$EXPECTED_URL"
    fi
else
    echo "Adding remote '$REMOTE_NAME'..."
    git remote add "$REMOTE_NAME" "https://github.com/$OWNER/$REPO.git"
fi

# Fetch from the remote
echo "Fetching from remote '$REMOTE_NAME'..."
git fetch "$REMOTE_NAME"

# Check if the branch exists on the remote
REMOTE_BRANCH="$REMOTE_NAME/$BRANCH_NAME"
if ! git show-ref --verify --quiet "refs/remotes/$REMOTE_BRANCH"; then
    echo "Error: Branch '$BRANCH_NAME' not found on remote '$REMOTE_NAME'"
    echo "Available branches on $REMOTE_NAME:"
    git branch -r | grep "$REMOTE_NAME/" | sed "s/.*$REMOTE_NAME\//  /"
    exit 1
fi

# Check if local branch already exists
if git show-ref --verify --quiet "refs/heads/$BRANCH_NAME"; then
    echo "Local branch '$BRANCH_NAME' already exists"
    echo "Switching to existing branch and updating..."
    git switch "$BRANCH_NAME"
    git reset --hard "$REMOTE_BRANCH"
else
    echo "Creating and switching to new branch '$BRANCH_NAME'..."
    git switch -c "$BRANCH_NAME" "$REMOTE_BRANCH"
fi

echo ""
echo "✅ Successfully checked out branch '$BRANCH_NAME'"
echo "Current branch: $(git branch --show-current)"
echo "Latest commit: $(git log -1 --oneline)"
