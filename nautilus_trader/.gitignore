*.c
*.o
*.so
*.exp
*.obj
*.lib
*.xml

# Git
*.diff

# Logs and patches
*.log
*.patch
log/
logs/

# Profiler and database
*.prof
*.rdb
*.sqlite

# Secrets and environment files
*.key
*.key_secret
*.env

# Compressed files
*.tar.gz*
*.zip

# Data
*.parquet

# IntelliJ and VSCode project
*.iml
.idea/
.vscode/

# Benchmarking and coverage
.benchmarks*
.coverage*

# Rust related
*target/

# Python related
*.pyc
*.pyd
.history*
.cache/
.codspeed/
.mypy_cache/
.profile/
.pytest_cache/
.ruff_cache/
*dask-worker-space*
requirements*
venv*/
__pycache__/

# Build environment and temp
_build/
build/
dist/
env/
*tmp/
*temp/

# System and editor specific
.cursorignore
.DS_Store
.null-ls*
CLAUDE.md
CODEX.md
PERF.JSON
output.json

# Nautilus specific
.ipynb_checkpoints/
bench_data/
/api_reference/
/catalog/
/docs/getting_started/catalog/
/docs/tutorials/catalog/
/docs/tutorials/databento/
/examples/backtest/notebooks/catalog/
/examples/notebooks/catalog/
nautilus_trader/**/.gitignore
nautilus_trader/test_kit/mocks/.nautilus/
nautilus_trader.egg-info
tests/test_data/catalog/
tests/test_data/large/*
tests/unit_tests/catalog/
tests/unit_tests/persistence/catalog/
tests/test_data/**/backtest/**

# Exceptions
!tests/test_data/large/checksums.json
!tests/test_data/nautilus/*/**.parquet
!tests/integration_tests/adapters/betfair/responses/*.log
!/nautilus_core/adapters/src/databento/test_data/*
