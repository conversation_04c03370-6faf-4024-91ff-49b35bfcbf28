# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

from decimal import Decimal

import pandas as pd
import pytz

from libc.stdint cimport uint64_t

from nautilus_trader.core.correctness cimport Condition
from nautilus_trader.core.datetime cimport format_iso8601
from nautilus_trader.core.rust.model cimport AssetClass
from nautilus_trader.core.rust.model cimport InstrumentClass
from nautilus_trader.core.rust.model cimport OrderSide
from nautilus_trader.model.functions cimport asset_class_from_str
from nautilus_trader.model.functions cimport asset_class_to_str
from nautilus_trader.model.functions cimport instrument_class_from_str
from nautilus_trader.model.functions cimport instrument_class_to_str
from nautilus_trader.model.functions cimport order_side_from_str
from nautilus_trader.model.functions cimport order_side_to_str
from nautilus_trader.model.identifiers cimport InstrumentId
from nautilus_trader.model.identifiers cimport Symbol
from nautilus_trader.model.instruments.base cimport Instrument
from nautilus_trader.model.instruments.base cimport Price
from nautilus_trader.model.objects cimport Currency
from nautilus_trader.model.objects cimport Quantity


cdef class ComboLeg:
    """
    Represents a single leg within an option combo.

    Parameters
    ----------
    instrument_id : InstrumentId
        The instrument ID for this leg.
    ratio : int
        The signed ratio/quantity multiplier for this leg.
        Positive values indicate BUY, negative values indicate SELL.
    weight : float, optional
        The weight for pricing calculations (defaults to ratio).
    """



    def __init__(
        self,
        InstrumentId instrument_id not None,
        int ratio,
        double weight = 0.0,
    ) -> None:
        Condition.not_equal(ratio, 0, "ratio", "zero")

        self.instrument_id = instrument_id
        self.ratio = ratio

        # Calculate weight if not provided
        if weight == 0.0:
            self.weight = float(ratio)
        else:
            self.weight = weight

    @property
    def side(self) -> OrderSide:
        """Return the order side based on ratio sign."""
        return OrderSide.BUY if self.ratio > 0 else OrderSide.SELL

    @property
    def abs_ratio(self) -> int:
        """Return the absolute value of the ratio."""
        return abs(self.ratio)

    def __repr__(self) -> str:
        return (
            f"{type(self).__name__}("
            f"instrument_id={self.instrument_id}, "
            f"ratio={self.ratio}, "
            f"weight={self.weight})"
        )

    def __str__(self) -> str:
        side_str = "BUY" if self.ratio > 0 else "SELL"
        return f"{side_str} {abs(self.ratio)} {self.instrument_id}"


cdef class OptionCombo(Instrument):
    """
    Represents a multi-leg option combination instrument (2-4 legs).
    
    This instrument type allows trading of option combinations like iron condors,
    butterflies, straddles, etc. The combo is priced as a linear combination of
    its component legs, with bid/ask spreads based on vega-weighted sizing.
    
    Parameters
    ----------
    instrument_id : InstrumentId
        The instrument ID for the combo.
    raw_symbol : Symbol
        The raw/local/native symbol for the instrument.
    asset_class : AssetClass
        The option combo asset class.
    currency : Currency
        The combo currency.
    price_precision : int
        The price decimal precision.
    price_increment : Price
        The minimum price increment (tick size).
    multiplier : Quantity
        The combo multiplier.
    lot_size : Quantity
        The rounded lot unit size.
    underlying : str
        The underlying asset.
    strategy_type : str
        The strategy type (e.g., "IRON_CONDOR", "BUTTERFLY", "STRADDLE").
    legs : list[ComboLeg]
        The component legs (2-4 legs).
    vega_multiplier : float
        Multiplier for vega-based bid/ask spread calculation.
    activation_ns : uint64_t
        UNIX timestamp (nanoseconds) for contract activation.
    expiration_ns : uint64_t
        UNIX timestamp (nanoseconds) for contract expiration.
    ts_event : uint64_t
        UNIX timestamp (nanoseconds) when the data event occurred.
    ts_init : uint64_t
        UNIX timestamp (nanoseconds) when the data object was initialized.
    margin_init : Decimal, optional
        The initial margin requirement.
    margin_maint : Decimal, optional
        The maintenance margin requirement.
    maker_fee : Decimal, optional
        The maker fee rate.
    taker_fee : Decimal, optional
        The taker fee rate.
    exchange : str, optional
        The exchange MIC code.
    info : dict, optional
        Additional instrument information.
    
    Raises
    ------
    ValueError
        If legs list is empty or has more than 4 legs.
    ValueError
        If strategy_type is not a valid string.
    ValueError
        If vega_multiplier is not positive.
    """
    

    
    def __init__(
        self,
        InstrumentId instrument_id not None,
        Symbol raw_symbol not None,
        AssetClass asset_class,
        Currency currency not None,
        int price_precision,
        Price price_increment not None,
        Quantity multiplier not None,
        Quantity lot_size not None,
        str underlying,
        str strategy_type,
        list legs not None,
        double vega_multiplier,
        uint64_t activation_ns,
        uint64_t expiration_ns,
        uint64_t ts_event,
        uint64_t ts_init,
        margin_init: Decimal | None = None,
        margin_maint: Decimal | None = None,
        maker_fee: Decimal | None = None,
        taker_fee: Decimal | None = None,
        str exchange = None,
        dict info = None,
    ) -> None:
        # Validation
        Condition.valid_string(underlying, "underlying")
        Condition.valid_string(strategy_type, "strategy_type")
        Condition.not_empty(legs, "legs")
        Condition.is_true(len(legs) >= 2, "legs must have at least 2 elements")
        Condition.is_true(len(legs) <= 4, "legs must have at most 4 elements")
        Condition.positive(vega_multiplier, "vega_multiplier")
        
        if exchange is not None:
            Condition.valid_string(exchange, "exchange")
        
        # Validate all legs are ComboLeg instances
        for leg in legs:
            Condition.type(leg, ComboLeg, "leg")
        
        super().__init__(
            instrument_id=instrument_id,
            raw_symbol=raw_symbol,
            asset_class=asset_class,
            instrument_class=InstrumentClass.OPTION_SPREAD,  # Reuse existing class
            quote_currency=currency,
            is_inverse=False,
            price_precision=price_precision,
            size_precision=0,  # No fractional contracts
            price_increment=price_increment,
            size_increment=Quantity.from_int_c(1),
            multiplier=multiplier,
            lot_size=lot_size,
            max_quantity=None,
            min_quantity=Quantity.from_int_c(1),
            max_notional=None,
            min_notional=None,
            max_price=None,
            min_price=None,
            margin_init=margin_init or Decimal(0),
            margin_maint=margin_maint or Decimal(0),
            maker_fee=maker_fee or Decimal(0),
            taker_fee=taker_fee or Decimal(0),
            ts_event=ts_event,
            ts_init=ts_init,
        )
        
        self.underlying = underlying
        self.strategy_type = strategy_type
        self.legs = legs
        self.vega_multiplier = vega_multiplier
        self.activation_ns = activation_ns
        self.expiration_ns = expiration_ns
        self.exchange = exchange
        self.info = info or {}

    @property
    def activation(self):
        """
        Return the activation datetime (UTC).

        Returns
        -------
        pd.Timestamp
        """
        return pd.Timestamp(self.activation_ns, tz=pytz.UTC)

    @property
    def expiration(self):
        """
        Return the expiration datetime (UTC).

        Returns
        -------
        pd.Timestamp
        """
        return pd.Timestamp(self.expiration_ns, tz=pytz.UTC)

    @property
    def is_expired(self):
        """
        Return whether the combo is expired.

        Returns
        -------
        bool
        """
        return self.expiration_ns <= pd.Timestamp.utcnow().value

    def get_leg_instrument_ids(self):
        """
        Return the instrument IDs of all legs.

        Returns
        -------
        list[InstrumentId]
        """
        return [leg.instrument_id for leg in self.legs]

    def calculate_combo_price(self, leg_prices: dict):
        """
        Calculate the combo price as a linear combination of leg prices.

        Parameters
        ----------
        leg_prices : dict[InstrumentId, Price]
            Dictionary mapping leg instrument IDs to their current prices.

        Returns
        -------
        Price or None
            The calculated combo price, or None if any leg price is missing.
        """
        if not leg_prices:
            return None

        combo_value = 0.0
        for leg in self.legs:
            leg_price = leg_prices.get(leg.instrument_id)
            if leg_price is None:
                return None
            combo_value += leg.weight * float(leg_price)

        return Price.from_str_c(f"{combo_value:.{self.price_precision}f}")

    def calculate_vega_weighted_spread(self, leg_vegas: dict, base_spread: float = 0.01):
        """
        Calculate bid/ask spread based on vega-weighted sizing.

        Parameters
        ----------
        leg_vegas : dict[InstrumentId, float]
            Dictionary mapping leg instrument IDs to their vega values.
        base_spread : float, default 0.01
            Base spread percentage to apply.

        Returns
        -------
        float
            The calculated spread multiplier.
        """
        if not leg_vegas:
            return base_spread

        total_vega = 0.0
        for leg in self.legs:
            leg_vega = leg_vegas.get(leg.instrument_id, 0.0)
            total_vega += abs(leg.weight * leg_vega)

        return base_spread * (1.0 + total_vega * self.vega_multiplier)

    def __repr__(self) -> str:
        return (
            f"{type(self).__name__}("
            f"id={self.id}, "
            f"underlying={self.underlying}, "
            f"strategy_type={self.strategy_type}, "
            f"legs={len(self.legs)})"
        )

    def __str__(self) -> str:
        legs_str = ", ".join(str(leg) for leg in self.legs)
        return f"{self.strategy_type} {self.underlying} [{legs_str}]"

    @staticmethod
    cdef OptionCombo from_dict_c(dict values):
        """
        Return an option combo from the given initialization values.

        Parameters
        ----------
        values : dict[str, object]
            The values for initialization.

        Returns
        -------
        OptionCombo
        """
        # Parse legs
        legs_data = values["legs"]
        legs = []
        for leg_data in legs_data:
            # Handle both old format (side + ratio) and new format (signed ratio)
            if "side" in leg_data:
                # Old format: convert side + ratio to signed ratio
                side = order_side_from_str(leg_data["side"])
                ratio = leg_data["ratio"]
                signed_ratio = ratio if side == OrderSide.BUY else -ratio
            else:
                # New format: use signed ratio directly
                signed_ratio = leg_data["ratio"]

            leg = ComboLeg(
                instrument_id=InstrumentId.from_str_c(leg_data["instrument_id"]),
                ratio=signed_ratio,
                weight=leg_data.get("weight", 0.0),
            )
            legs.append(leg)

        return OptionCombo(
            instrument_id=InstrumentId.from_str_c(values["id"]),
            raw_symbol=Symbol(values["raw_symbol"]),
            asset_class=asset_class_from_str(values["asset_class"]),
            currency=Currency.from_str_c(values["quote_currency"]),
            price_precision=values["price_precision"],
            price_increment=Price.from_str_c(values["price_increment"]),
            multiplier=Quantity.from_str_c(values["multiplier"]),
            lot_size=Quantity.from_str_c(values["lot_size"]),
            underlying=values["underlying"],
            strategy_type=values["strategy_type"],
            legs=legs,
            vega_multiplier=values["vega_multiplier"],
            activation_ns=values["activation_ns"],
            expiration_ns=values["expiration_ns"],
            ts_event=values["ts_event"],
            ts_init=values["ts_init"],
            margin_init=Decimal(values["margin_init"]) if values.get("margin_init") is not None else None,
            margin_maint=Decimal(values["margin_maint"]) if values.get("margin_maint") is not None else None,
            maker_fee=Decimal(values["maker_fee"]) if values.get("maker_fee") is not None else None,
            taker_fee=Decimal(values["taker_fee"]) if values.get("taker_fee") is not None else None,
            exchange=values.get("exchange"),
            info=values.get("info"),
        )

    @staticmethod
    def from_dict(dict values) -> OptionCombo:
        """
        Return an option combo from the given initialization values.

        Parameters
        ----------
        values : dict[str, object]
            The values for initialization.

        Returns
        -------
        OptionCombo
        """
        return OptionCombo.from_dict_c(values)

    cpdef dict to_dict(self):
        """
        Return a dictionary representation of this instrument.

        Returns
        -------
        dict[str, object]
        """
        legs_data = []
        for leg in self.legs:
            legs_data.append({
                "instrument_id": str(leg.instrument_id),
                "ratio": leg.ratio,  # Now stores signed ratio directly
                "weight": leg.weight,
            })

        return {
            "type": "OptionCombo",
            "id": str(self.id),
            "raw_symbol": str(self.raw_symbol),
            "asset_class": asset_class_to_str(self.asset_class),
            "quote_currency": str(self.quote_currency),
            "is_inverse": self.is_inverse,
            "price_precision": self.price_precision,
            "price_increment": str(self.price_increment),
            "multiplier": str(self.multiplier),
            "lot_size": str(self.lot_size),
            "underlying": self.underlying,
            "strategy_type": self.strategy_type,
            "legs": legs_data,
            "vega_multiplier": self.vega_multiplier,
            "activation_ns": self.activation_ns,
            "expiration_ns": self.expiration_ns,
            "ts_event": self.ts_event,
            "ts_init": self.ts_init,
            "margin_init": str(self.margin_init) if self.margin_init else None,
            "margin_maint": str(self.margin_maint) if self.margin_maint else None,
            "maker_fee": str(self.maker_fee) if self.maker_fee else None,
            "taker_fee": str(self.taker_fee) if self.taker_fee else None,
            "exchange": self.exchange,
            "info": self.info,
        }
