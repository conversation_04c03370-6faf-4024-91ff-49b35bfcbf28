# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

from libc.stdint cimport uint64_t

from nautilus_trader.core.rust.model cimport OrderSide
from nautilus_trader.model.identifiers cimport InstrumentId
from nautilus_trader.model.instruments.base cimport Instrument


cdef class ComboLeg:
    cdef readonly InstrumentId instrument_id
    cdef readonly OrderSide side
    cdef readonly int ratio
    cdef readonly double weight


cdef class OptionCombo(Instrument):
    cdef readonly str underlying
    cdef readonly str strategy_type
    cdef readonly list legs
    cdef readonly double vega_multiplier
    cdef readonly uint64_t activation_ns
    cdef readonly uint64_t expiration_ns
    cdef readonly str exchange
    cdef readonly dict info
    
    @staticmethod
    cdef OptionCombo from_dict_c(dict values)
    
    cpdef dict to_dict(self)
