[project]
name = "nautilus_trader"
version = "1.219.0"
description = "A high-performance algorithmic trading platform and event-driven backtester"
authors = [
    {name = "Nautech Systems", email = "<EMAIL>"},
]
classifiers = [
    "License :: OSI Approved :: GNU Lesser General Public License v3 or later (LGPLv3+)",
    "Operating System :: OS Independent",
    "Development Status :: 4 - Beta",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering",
    "Topic :: Office/Business :: Financial",
    "Topic :: Office/Business :: Financial :: Investment",
    "Intended Audience :: Developers",
    "Intended Audience :: Financial and Insurance Industry",
    "Intended Audience :: Science/Research",
]
license = {text = "LGPL-3.0-or-later"}
readme = "README.md"
requires-python = ">=3.11,<3.14"
dependencies = [
    "click>=8.2.1,<9.0.0",
    "fsspec>=2025.2.0,<2026.0.0",
    "msgspec>=0.19.0,<1.0.0",
    "numpy>=1.26.4",
    "pandas>=2.2.3,<3.0.0",
    "portion>=2.6.1",
    "pyarrow>=20.0.0",
    "pytz>=2025.1.0",
    "tqdm>=4.67.1,<5.0.0",
    "uvloop>=0.21.0,<1.0.0; sys_platform != \"win32\"",
]

[project.urls]
homepage = "https://nautilustrader.io"
repository = "https://github.com/nautechsystems/nautilus_trader"
docs = "https://nautilustrader.io/docs"

# For now we use the poetry build backend until uv supports custom build scripts
[build-system]
requires = [
    "setuptools>=80",
    "poetry-core>=2.0.1",
    "numpy>=1.26.4",
    "cython==3.1.2",
]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
include = [
    # Rust source must be included in the source distributions
    { path = "crates/*", format = "sdist" },
    { path = "Cargo.lock", format = "sdist" },
    { path = "Cargo.toml", format = "sdist" },
    # Compiled extensions must be included in the wheel distributions
    { path = "nautilus_trader/**/*.so", format = "wheel" },
    { path = "nautilus_trader/**/*.pyd", format = "wheel" },
    # Include the py.typed file for type checking support
    { path = "nautilus_trader/py.typed", format = "sdist" },
    { path = "nautilus_trader/py.typed", format = "wheel" },
    # Include Python interface files for type checking support
    { path = "nautilus_trader/**/*.pyi", format = "sdist" },
    { path = "nautilus_trader/**/*.pyi", format = "wheel" },
]

[tool.poetry.build]
script = "build.py"
generate-setup-file = false

[project.optional-dependencies]
betfair = [
    "betfair-parser==0.14.4",
]
ib = [
    "defusedxml>=0.7.1,<1.0.0",
    "nautilus-ibapi==10.30.1",
]
docker = [
    "docker>=7.1.0,<8.0.0",
]
dydx = [
    "v4-proto==7.0.5",
    "grpcio==1.68.1",
    "protobuf==5.29.1",
    "bech32>=1.2.0,<2.0.0",
    "ecdsa>=0.19.0,<1.0.0",
    "bip-utils>=2.9.3,<3.0.0",
    "pycryptodome>=3.20.0,<4.0.0",
]
polymarket = [
    "py-clob-client==0.23.0,<1.0.0",  # Pinned to 0.23.0 for stability
]

[dependency-groups]
dev = [
    "cython==3.1.2",
    "setuptools>=75",
    "black>=25.1.0,<26.0.0",
    "docformatter>=1.7.7,<2.0.0",
    "mypy>=1.16.1,<2.0.0",
    "pandas-stubs>=2.3.0,<3.0.0",
    "pre-commit>=4.2.0,<5.0.0",
    "requests>=2.32.4,<3.0.0",
    "ruff>=0.12.1,<1.0.0",
    "types-pytz>=2024.2,<2025.0",
    "types-requests>=2.32,<3.0",
    "types-toml>=0.10.2,<1.0.0",
]
test = [
    "aiohttp==3.11.18,<4.0.0",  # Pinned to 3.11.8 for wheel compatibility on supported platforms
    "coverage>=7.9.1,<8.0.0",
    "pytest>=7.4.4,<8.0.0",
    "pytest-aiohttp>=1.1.0,<2.0.0",
    "pytest-asyncio==0.21.1",
    "pytest-benchmark>=4.0.0,<5.0.0",
    "pytest-codspeed>=3.2.0,<4.0.0",
    "pytest-cov>=4.1.0,<5.0.0",
    "pytest-mock>=3.14.1,<4.0.0",
    "pytest-xdist[psutil]>=3.8.0,<4.0.0",
]
docs = [
    "numpydoc>=1.9.0,<2.0.0",
    "linkify-it-py>=2.0.3,<3.0.0",
    "myst-parser>=4.0.1,<5.0.0",
    "sphinx-comments>=0.0.3,<1.0.0",
    "sphinx-markdown-builder>=0.6.8,<1.0.0",
]

[tool.isort]  # Used by legacy isort for Cython modules
py_version = "311"
skip_glob = ["**/core/rust/*"]
combine_as_imports = true
line_length = 100
ensure_newline_before_comments = true
force_single_line = true
include_trailing_comma = true
multi_line_output = 3
lines_after_imports = 2
use_parentheses = true
filter_files = true

[tool.black]
target_version = ["py311", "py312"]
line_length = 100

[tool.docformatter]
black = true
make-summary-multi-line = true
pre-summary-new-line = true
blank = true
recursive = true
in-place = true

[tool.ruff]
target-version = "py311"
line-length = 150  # Reduce to 100

exclude = [
    ".benchmarks",
    ".eggs",
    ".git",
    ".mypy_cache",
    ".pytest_cache",
    ".ruff_cache",
    ".venv",
    "build",
    "dist",
    "venv",
]

[tool.ruff.lint]
select = [
    "C4",
    "E",
    "F",
    "W",
    "C90",
    "D",
    # "DTZ",
    "UP",
    "S",
    "T10",
    "ICN",
    "PIE",
    # "PT",
    "PYI",
    "Q",
    "I",
    "RSE",
    "TID",
    # "SIM",
    # "ARG",
    # "ERA",
    "PD",
    # "PGH",
    # "PLW",
    "NPY",
    "RUF",
]

ignore = [
    "D100",  # Missing docstring in public module  **fix**
    "D101",
    "D102",  # Missing docstring in public method  **fix**
    "D103",  # Missing docstring in public function  **fix**
    "D104",  # Missing docstring in public package  **fix**
    "D107",
    "D105",
    "D200",  # One-line docstring should fit on one line with quotes (optional style)
    "D203",  # 1 blank line required before class docstring (optional style)
    "D205",  # 1 blank line required between summary line and description (optional style)
    "D212",  # Multi-line docstring summary should start at the first line (optional style)
    "D400",  # First line should end with a period (not always a first line)
    "D413",  # Missing blank line after last section ('Parameters')
    "D415",  # First line should end with a period, question mark, or exclamation point (not always a first line)
    "D416",  # Section name should end with a colon ('Warnings:', not 'Warnings') (incorrect?)
    "E741",  # Ambiguous variable name (single char)
    "PD901", # `df` is a bad variable name. Be kinder to your future self
    "RUF012",  # Mutable class attributes should be annotated with `typing.ClassVar`
    "S101",  # Use of assert detected (OK in test suite)
    "S105",  # Use of hard-coded password (spurious)
    "S106",  # Use of hard-coded password (spurious)
    "S113",  # Probable use of requests call without timeout **fix**
    "S603",  # `subprocess` call: check for execution of untrusted input **fix**
]

# Allow autofix for all enabled rules (when `--fix`) is provided
fixable = ["ALL"]

unfixable = []
# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.isort]
force-single-line = true
single-line-exclusions = ["typing"]
lines-after-imports = 2

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.ruff.lint.per-file-ignores]
"test_perf_logger.py" = ["S311"]

[tool.mypy]
python_version = "3.11"
disallow_incomplete_defs = true
explicit_package_bases = true
ignore_missing_imports = true
namespace_packages = true
no_strict_optional = false
warn_no_return = true
warn_unused_configs = true
warn_unused_ignores = true

[[tool.mypy.overrides]]
no_strict_optional = true
module = [
    "examples/*",
    "nautilus_trader/adapters/betfair/*",
    "nautilus_trader/adapters/binance/*",
    "nautilus_trader/adapters/interactive_brokers/*",
    "nautilus_trader/indicators/ta_lib/*",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = "-ra --new-first --failed-first --doctest-modules --doctest-glob=\"*.pyx\""
asyncio_mode = "strict"
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

[tool.coverage.run]
plugins = ["Cython.Coverage"]
source = ["nautilus_trader"]
omit = [
    "nautilus_trader/adapters/*",
    "nautilus_trader/examples/*",
    "nautilus_trader/test_kit/*",
]

[tool.coverage.report]
fail_under = 0
show_missing = true
